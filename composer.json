{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0", "akaunting/laravel-setting": "^1.2", "astrotomic/laravel-translatable": "^11.9", "barryvdh/laravel-dompdf": "^2.1", "buglinjo/laravel-webp": "^2.0", "doctrine/dbal": "^2.12", "dompdf/dompdf": "^2.0", "fideloper/proxy": "^4.2", "fruitcake/laravel-cors": "^2.0", "google/apiclient": "^2.14", "guzzlehttp/guzzle": "^7.0.1", "kornrunner/keccak": "^1.1", "kub-at/php-simple-html-dom-parser": "^1.9", "laravel/framework": "^8.0", "laravel/passport": "^10.1", "laravel/socialite": "^5.12", "laravel/tinker": "^2.0", "laravel/ui": "^3.1", "league/oauth2-google": "^4.0", "maatwebsite/excel": "^3.1", "mcamara/laravel-localization": "^1.6", "mpdf/mpdf": "^8.2", "nette/php-generator": "^3.4", "pion/laravel-chunk-upload": "^1.4", "rap2hpoutre/fast-excel": "^5.2", "simplito/elliptic-php": "^1.0", "socialiteproviders/linkedin": "^5.0", "spatie/laravel-permission": "^3.17", "sunra/php-simple-html-dom-parser": "^1.5", "thunderer/shortcode": "^0.7.4", "torann/geoip": "^3.0"}, "require-dev": {"facade/ignition": "^2.3.6", "fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Sina\\Shuttle\\": "packages/sina/shuttle/src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Sina\\Shuttle\\": "packages/sina/shuttle/src/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}