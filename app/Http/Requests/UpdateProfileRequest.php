<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $userId = auth()->id();
        
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'email' => [
                'required',
                'email',
                Rule::unique('users')->ignore($userId),
            ],
            'mobile' => [
                'required',
                'string',
                Rule::unique('users')->ignore($userId),
            ],
            'address' => 'nullable|string|max:255',
            'password' => 'nullable|string|min:8|confirmed',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'first_name.required' => __('common.validation_required', ['attribute' => __('common.first_name')]),
            'email.required' => __('common.validation_required', ['attribute' => __('common.email')]),
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => __('common.validation_unique', ['attribute' => __('common.email')]),
            'mobile.required' => __('common.validation_required', ['attribute' => __('common.mobile')]),
            'mobile.unique' => __('common.validation_unique', ['attribute' => __('common.mobile')]),
            'password.min' => 'Password must be at least 8 characters.',
            'password.confirmed' => __('common.password_must_match'),
            'image.image' => 'The file must be an image.',
            'image.mimes' => 'The image must be a file of type: jpeg, png, jpg, gif.',
            'image.max' => 'The image may not be greater than 2MB.',
        ];
    }
}
