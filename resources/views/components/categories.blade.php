<section class="section courses mt-80 color-bg no-c" data-bg="{{data_get($data,"hex")}}"><div class="container"><div class="row"><div class="col-md-12"><div class="section-title"><span>{{data_get($data,"t2")}}</span><h1>{{data_get($data,"t1")}}</h1><ul><li class="sprev"><i class="icofont-rounded-left"></i></li><li class="snext"><i class="icofont-rounded-right"></i></li></ul></div><div class="courses-slider"><div class="swiper-container courses-slider__slider" id="slider"><div class="swiper-wrapper">@foreach(\App\Models\Category::query()->onlyWithCourse()->withCount('courses')->get() as $cat)<div class="swiper-slide"><div class="courses-slider__item"><a href="{{route('course.index')}}?category_id={{$cat->id}}"><figure><img src="{{Storage::url($cat->image)}}" alt="" /></figure><div class="title-in"><h2>{{$cat->name}}</h2><span>{{$cat->courses_count}} Course</span></div></a></div></div>@endforeach</div></div></div></div></div> </div></section>