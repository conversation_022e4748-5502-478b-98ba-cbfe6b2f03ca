@extends('app')

@push("seo")
    <title>Reset Password - Business-Eagles.com</title>
    <meta name="description" content="Create a new password for your Business Eagles account. Enter your new password to complete the reset process.">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="{{asset('css/apple-touch-icon.png')}}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{asset('css/favicon-32x32.png')}}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{asset('css/favicon-16x16.png')}}">
    <link rel="manifest" href="{{asset('css/site.webmanifest')}}">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="theme-color" content="#ffffff">

    <!-- Open Graph -->
    <meta property="og:title" content="Reset Password - Business Eagles">
    <meta property="og:description" content="Create a new password for your Business Eagles account securely.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:site_name" content="Business Eagles">
@endpush

@section('content')
    @include('includes.header')
    <section class="speadbar">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="speadbar-title">
                        <ul>
                            <li><svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M18.4384 20C19.3561 20 20.1493 19.3726 20.2725 18.4632C20.3895 17.5988 20.5 16.4098 20.5 15C20.5 12 20.6683 10.1684 17.5 7C16.0386 5.53865 14.4064 4.41899 13.3024 3.74088C12.4978 3.24665 11.5021 3.24665 10.6975 3.74088C9.5935 4.41899 7.96131 5.53865 6.49996 7C3.33157 10.1684 3.49997 12 3.49997 15C3.49997 16.4098 3.61039 17.5988 3.72745 18.4631C3.85061 19.3726 4.64378 20 5.56152 20H18.4384Z" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg></li>
                            <li class=""><a href="/" title="Home">Home</a></li>
                            <li class=" active "><a href="#" title="Reset Password">Reset Password</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="section login">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <div class="login-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" width="692" height="499.87671" viewBox="0 0 692 499.87671" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M945,696.10473H255a1,1,0,0,1,0-2H945a1,1,0,0,1,0,2Z" transform="translate(-254 -200.06165)" fill="#ccc"/><path d="M876.70437,521.703h-406a6.5,6.5,0,1,0,0,13h11.5v155.5a6.5,6.5,0,0,0,13,0l.09878-155.5H851.20437v155.5a6.5,6.5,0,0,0,13,0V538.13036l1.89111-3.42737h10.60889a6.5,6.5,0,1,0,0-13Z" transform="translate(-254 -200.06165)" fill="#ccc"/><path d="M695.79406,525.10473H572.81224a8.992,8.992,0,0,1-8.98182-8.98182V448.41382a8.992,8.992,0,0,1,8.98182-8.98182H695.79406a8.992,8.992,0,0,1,8.98182,8.98182v67.70909A8.992,8.992,0,0,1,695.79406,525.10473Z" transform="translate(-254 -200.06165)" fill="#3f3d56"/><path d="M695.79338,446.34472H572.81292a2.07422,2.07422,0,0,0-2.06983,2.07v67.71a2.07427,2.07427,0,0,0,2.06983,2.07H695.79338a2.07427,2.07427,0,0,0,2.06983-2.07v-67.71A2.07422,2.07422,0,0,0,695.79338,446.34472Z" transform="translate(-254 -200.06165)" fill="#fff"/><circle cx="381.0242" cy="273.92427" r="13.88708" fill="#a0616a"/></svg>
                </div>
                <!-- /.login-icon -->
            </div>
            <!-- /.col-md-6 -->
            <div class="col-md-6">
                <div class="login-form">
                    <div class="login-form__content">
                        <div class="auth-content">
                            <div class="loginform">
                                <h1 style="margin-bottom:25px;">Reset Password</h1>
                                <p style="margin-bottom:20px;">Enter your new password below.</p>
                                @if ($errors->any())
                                    <div class="alert alert-danger" style="background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                                        <strong>❌ Error!</strong>
                                        @foreach ($errors->all() as $error)
                                            <br>{{ $error }}
                                        @endforeach
                                    </div>
                                @endif

                                @if (session('status'))
                                    <div class="alert alert-success" role="alert" id="success-message" style="background-color: #d4edda; border-color: #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                                        <strong>🎉 Success!</strong> {{ session('status') }}
                                        <br><small style="color: #6c757d;">You will be redirected to the home page in <span id="countdown" style="font-weight: bold; color: #007cba;">60</span> seconds.</small>
                                    </div>
                                    <script>
                                        let countdown = 60;
                                        const countdownElement = document.getElementById('countdown');
                                        const timer = setInterval(function() {
                                            countdown--;
                                            countdownElement.textContent = countdown;
                                            if (countdown <= 0) {
                                                clearInterval(timer);
                                                window.location.href = '/';
                                            }
                                        }, 1000);
                                    </script>
                                @endif
                                <form method="POST" action="{{ route('password.update') }}" class="valid-form">
                                    @csrf
                                    <input type="hidden" name="token" value="{{ $token }}">

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="loginform-form">
                                                <!-- <label for="email" style="font-size: 12px; color: #6c757d; margin-bottom: 5px;">Email Address (cannot be changed)</label> -->
                                                <input id="email" type="email" class="f-input @error('email') is-invalid @enderror"
                                                       name="email" value="{{ $email ?? old('email') }}" required autocomplete="email" readonly
                                                       style="background-color: #f8f9fa; cursor: not-allowed; color: #6c757d; border: 1px solid #dee2e6;">
                                                @error('email')
                                                    <div class="text-danger">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="loginform-form">
                                                <input id="password" type="password" class="f-input @error('password') is-invalid @enderror"
                                                       name="password" placeholder="New Password" required autocomplete="new-password" data-validation="required">
                                                @error('password')
                                                    <div class="text-danger">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="loginform-form">
                                                <input id="password-confirm" type="password" class="f-input"
                                                       name="password_confirmation" placeholder="Confirm Password" required autocomplete="new-password" data-validation="required">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="loginform-btns">
                                                <button type="submit" class="btn-submit">Reset Password</button>
                                                <a class="btn-register" href="{{ route('login') }}" title="Back to Login">Back to Login</a>
                                            </div>
                                        </div>
                                    </div>
                                </form>

                                <script>
                                    // Ensure email field stays readonly
                                    document.getElementById('email').addEventListener('focus', function() {
                                        this.blur();
                                    });

                                    // Prevent any attempts to modify email field
                                    document.getElementById('email').addEventListener('keydown', function(e) {
                                        e.preventDefault();
                                        return false;
                                    });
                                </script>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.login-form -->
            </div>
            <!-- /.col-md-6 -->
        </div>
    </div>
</section>
@stop
